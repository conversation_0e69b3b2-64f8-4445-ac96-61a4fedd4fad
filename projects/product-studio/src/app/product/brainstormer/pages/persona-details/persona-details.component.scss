:host {
  display: block;
}

// Page Layout & Header
.page-container {
  background: rgba(101, 102, 205, 0.12);
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.top-header-gradient {
  height: 0.5rem;
  background: linear-gradient(90deg, #8a2be2, #4a00e0);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.avatar-sm {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}
.avatar-md {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

// Left Summary Card
.persona-summary-card {
  position: relative; // Needed for absolute positioning of the edit button
  border-radius: 12px;
  border: none;
  border-radius: 12px;
  border: 1px solid var(--LightMode-Container-Stroke, #e4e7ec);
  background: var(--LightMode-Container, #fff);
  .card-body {
    padding: 1.5rem;

    /* Where are you? */
    box-shadow:
      0px 4px 8px -2px rgba(16, 24, 40, 0.1),
      0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  }
}

// **** STYLE FOR THE RESTORED BUTTON ****
.btn-edit-main {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 2;
  background: transparent;
  border: none;
  padding: 0.25rem;
  line-height: 1;

  img {
    width: 16px;
    height: 16px;
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
  }

  &:hover img {
    opacity: 1;
  }
}

.profile-section {
  text-align: center;
  margin-bottom: 1.5rem;
  .avatar-wrapper {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    padding: 5px;
    background: linear-gradient(135deg, #fbcfe8, #9b59b6);
  }
  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  .role-title {
    color: #4a00e0;
    font-size: 1.5rem;
    font-weight: bold;
  }
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    span:first-child {
      color: #6c757d;
    }
    span:last-child {
      font-weight: 500;
    }
  }
}

.quote-section {
  padding: 6px 1rem 1rem;
  border-radius: 8px;
  position: relative;
  // margin-bottom: 1.5rem;
  .quote-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    opacity: 0.6;
  }
  .quote-text {
    padding-left: 2rem;
    margin: 0;
    color: #6c757d;
    font-style: italic;
  }
}

.personality-section {
  .personality-title {
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
  }
  .personality-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    .tag {
      padding: 0.3rem 0.8rem;
      border-radius: 16px;
      font-size: 0.8rem;
      &:nth-child(3n + 1) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
      &:nth-child(3n + 2) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
      &:nth-child(3n + 3) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
    }
  }
}

// Right Detail Cards
.detail-card {
  border: none;
  border-radius: 12px;
  border: 1px solid var(--LightMode-Container-Stroke, #e4e7ec);
  background: var(--LightMode-Container, #fff);
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  .card-header {
    background: none;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0.5rem;
    h3 {
      font-size: 1.1rem;
      font-weight: bold;
      margin: 0;
    }
  }
  .btn-edit {
    background: transparent;
    border: none;
    padding: 0.25rem;
    img {
      width: 16px;
      height: 16px;
      opacity: 0.6;
      transition: opacity 0.2s;
    }
    &:hover img {
      opacity: 1;
    }
  }
  .card-body {
    padding: 0.5rem 1.5rem 1.5rem;
    ul {
      list-style: none;
      padding-left: 0;
      margin: 0;
    }
    li {
      position: relative;
      padding-left: 1.25rem;
      margin-bottom: 0.5rem;
    }
    li::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #4a00e0;
      font-weight: bold;
    }
  }
}

.device-body {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  min-height: 120px;
  img {
    height: 48px;
    filter: grayscale(30%) opacity(0.8);
  }
}

// Progress Bar Gradients
.progress {
  background-color: #e9ecef;
  border-radius: 10px;
}
.progress-bar {
  &.bg-primary {
    background: linear-gradient(90deg, #8a2be2, #4a00e0);
  }
  &.bg-info {
    background: linear-gradient(90deg, #8a2be2, #4a00e0);
  }
  &.bg-success {
    background: linear-gradient(990deg, #8a2be2, #4a00e0);
  }
}

// Persona Selector Dropdown
.persona-selector-card {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  width: 300px;
  padding-top: 8px;
  .list-group-item-action:hover {
    background-color: #f0e6ff;
  }
}

// Dialog Panel Style
::ng-deep
  .custom-dialog-container-no-padding
  .mat-mdc-dialog-container
  .mdc-dialog__surface {
  padding: 0 !important;
  border-radius: 1rem !important;
}
