<div class="page-container">
  <!-- Top Decorative Header Bar -->
  <!-- <div class="top-header-gradient"></div> -->

  <!-- Main Header with Navigation -->
  <header class="page-header">
    <button class="btn btn-light d-flex align-items-center" (click)="goBack()">
        <awe-icons iconName="awe_chevron_left" iconSize="20px">Persona List</awe-icons>
    </button>
    <div class="position-relative" *ngIf="selectedPersona">
      <button class="btn btn-light border dropdown-toggle d-flex align-items-center" (click)="togglePersonaSelector()">
        <img [src]="selectedPersona.avatar" class="avatar-sm me-2">
        <!-- <awe-dropdown
          class="d-flex align-self-start"
          selectedValue="Quarter wise"
          [options]="timePeriodOptions"
          animation="rotateX"
          theme="light"
        ></awe-dropdown> -->
        <span>{{ selectedPersona.name || selectedPersona.role }}</span>
      </button>
      <div *ngIf="isPersonaSelectorOpen" class="persona-selector-card">
        <div class="card shadow-lg border-0">
          <!-- <div class="p-3 border-bottom"><h6 class="fw-bold mb-0">User Persona</h6></div> -->
          <div class="p-2">
            <button *ngFor="let p of personas" (click)="selectPersona(p.id)"
              class="list-group-item list-group-item-action border-0 rounded-3 p-2 mb-1">
              <div class="d-flex align-items-center">
                <img [src]="p.avatar" class="avatar-md me-3">
                <div>
                  <div class="fw-bold">{{ p.name }}</div>
                  <div class="text-muted small">{{ p.role }}</div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content Grid -->
  <div class="row g-3 d-flex justify-content-center align-items-center p-4">
    <!-- LEFT COLUMN: PERSONA SUMMARY CARD -->
    <div class="col-12 col-lg-2 col-xl-2 n" *ngIf="selectedPersona">
      <div class="card h-100 persona-summary-card">
        <div class="card-body">
          <div class="profile-section">
            <div class="avatar-wrapper">
              <img [src]="selectedPersona.avatar" class="avatar-img">
            </div>
            <h2 class="role-title">{{ selectedPersona.role }}</h2>
          </div>
          <div class="info-section">
            <div class="info-row"><span>Age</span><span>{{ selectedPersona.age }}</span></div>
            <div class="info-row"><span>Education</span><span>{{ selectedPersona.education }}</span></div>
            <div class="info-row"><span>Status</span><span>{{ selectedPersona.status }}</span></div>
            <div class="info-row"><span>Location</span><span>{{ selectedPersona.location }}</span></div>
            <div class="info-row"><span>Tech Literacy</span><span>{{ selectedPersona.techLiteracy }}</span></div>
          </div>
          <div class="quote-section">
            <img [src]="colon" alt="Quote" class="quote-icon">
            <p class="quote-text">{{ selectedPersona.quote }}</p>
          </div>
          <div class="personality-section">
            <h3 class="personality-title">Personality</h3>
            <div class="personality-tags">
              <span *ngFor="let trait of selectedPersona.personality" class="tag">{{ trait }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- RIGHT COLUMN: DETAIL CARDS -->
    <div class="col-12 col-lg-10 col-xl-10" *ngIf="selectedPersona">
      <div class="row g-3">
        <!-- Pain Points Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Pain Points</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('painPoints')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('painPoints')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('painPoints', 'Pain Points')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <ul><li *ngFor="let item of selectedPersona.painPoints">{{ item }}</li></ul>
            </div>
          </div>
        </div>

        <!-- Motivation Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Motivation</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('motivation')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('motivation')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('motivation', 'Motivation')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <ul><li *ngFor="let item of selectedPersona.motivation">{{ item }}</li></ul>
            </div>
          </div>
        </div>

        <!-- Goals Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Goals</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('goals')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('goals')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('goals', 'Goals')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <ul><li *ngFor="let item of selectedPersona.goals">{{ item }}</li></ul>
            </div>
          </div>
        </div>

        <!-- Expectations Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Expectations</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('expectations')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('expectations')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('expectations', 'Expectations')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <ul><li *ngFor="let item of selectedPersona.expectations">{{ item }}</li></ul>
            </div>
          </div>
        </div>

        <!-- Skills Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Skills</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('skills')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('skills')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('skills', 'Skills')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="d-flex flex-column gap-3 pt-2">
                <div *ngFor="let skill of selectedPersona.skills; let i = index">
                  <span class="small text-muted mb-1 d-block">{{ skill.name }}</span>
                  <div class="progress" style="height: 10px;">
                    <div class="progress-bar rounded" [ngClass]="getProgressBarClass(i)" [style.width.%]="skill.level"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Devices Card -->
        <div class="col-12 col-md-6">
          <div class="card detail-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h3>Devices</h3>
              <div class="position-relative">
                <button class="three-dots-btn" (click)="toggleDropdown('devices')">
                  <awe-icons iconName="more_vert" iconSize="20px"></awe-icons>
                </button>
                <div *ngIf="isDropdownOpen('devices')" class="dropdown-menu show">
                  <button class="dropdown-item" (click)="openEditModal('devices', 'Devices')">
                    <awe-icons iconName="edit" iconSize="16px" class="me-2"></awe-icons>Edit
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body device-body">
              <img *ngIf="selectedPersona.devices.includes('mobile')" [src]="MobileIcon" alt="Mobile">
              <img *ngIf="selectedPersona.devices.includes('laptop')" [src]="LaptopIcon" alt="Laptop">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Click Outside Handler -->
  <div *ngIf="openDropdownId" class="click-outside-overlay" (click)="closeAllDropdowns()"></div>

  <!-- Edit Modal -->
  <awe-modal *ngIf="isEditModalOpen" (closeModal)="closeEditModal()">
    <div awe-modal-header>
      <awe-heading variant="h4" type="bold">Edit {{ selectedCardForEdit?.title }}</awe-heading>
    </div>

    <div awe-modal-body>
      <div class="modal-form">
        <!-- Array Data (Pain Points, Goals, etc.) -->
        <div *ngIf="isArrayData(editData) && selectedCardForEdit?.type !== 'skills'">
          <div *ngFor="let item of editData; let i = index; trackBy: trackByIndex" class="mb-3">
            <div class="d-flex gap-2">
              <awe-input
                [(ngModel)]="editData[i]"
                [placeholder]="'Enter ' + selectedCardForEdit?.title?.toLowerCase() + ' item'"
                class="flex-grow-1">
              </awe-input>
              <awe-button
                buttonText="Remove"
                buttonType="danger"
                buttonSize="sm"
                (click)="removeArrayItem(i)">
              </awe-button>
            </div>
          </div>
          <awe-button
            buttonText="Add Item"
            buttonType="secondary"
            buttonSize="sm"
            (click)="addArrayItem()"
            class="mb-3">
          </awe-button>
        </div>

        <!-- Skills Data -->
        <div *ngIf="selectedCardForEdit?.type === 'skills'">
          <div *ngFor="let skill of editData; let i = index; trackBy: trackByIndex" class="mb-3">
            <div class="row g-2">
              <div class="col-8">
                <awe-input
                  [(ngModel)]="skill.name"
                  placeholder="Skill name"
                  class="w-100">
                </awe-input>
              </div>
              <div class="col-3">
                <awe-input
                  [(ngModel)]="skill.level"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="Level"
                  class="w-100">
                </awe-input>
              </div>
              <div class="col-1">
                <awe-button
                  buttonText="×"
                  buttonType="danger"
                  buttonSize="sm"
                  (click)="removeArrayItem(i)">
                </awe-button>
              </div>
            </div>
          </div>
          <awe-button
            buttonText="Add Skill"
            buttonType="secondary"
            buttonSize="sm"
            (click)="addArrayItem()"
            class="mb-3">
          </awe-button>
        </div>

        <!-- Regenerate Section -->
        <div class="regenerate-section mt-4">
          <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>
          <awe-input
            [(ngModel)]="regeneratePrompt"
            placeholder="Enter prompt to regenerate content..."
            class="mb-2">
          </awe-input>
          <awe-button
            buttonText="Regenerate"
            buttonType="primary"
            buttonSize="sm"
            (click)="onRegenerate()">
          </awe-button>
        </div>
      </div>
    </div>

    <div awe-modal-footer>
      <awe-button
        buttonText="Cancel"
        buttonType="secondary"
        (click)="closeEditModal()">
      </awe-button>
      <awe-button
        buttonText="Save Changes"
        buttonType="primary"
        (click)="saveCardData()">
      </awe-button>
    </div>
  </awe-modal>
</div>