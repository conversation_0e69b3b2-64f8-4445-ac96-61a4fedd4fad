import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { PersonaDataService, PersonaData, PersonaCard } from '../../services/persona-data.service';
import { HeadingComponent, IconsComponent, InputComponent, ButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-persona-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
    ButtonComponent
  ],
  templateUrl: './persona-details.component.html',
  styleUrls: ['./persona-details.component.scss']
})
export class PersonaDetailsComponent implements OnInit, OnDestroy {
  // Icons
  PencilEditIcon: string = '/icons/pencil-edit.svg';
  MobileIcon: string = '/mobile-icon/mobile-icon-light.svg';
  LaptopIcon: string = '/web-icon/web-icon-light.svg';
  colon: string = '/svgs/colon.svg';
  InlargeIcon: string = '/svgs/inlarge-icon.svg';
  threeDotsIcon: string = '/icons/three-dot.svg';

  // State
  public isPersonaSelectorOpen = false;

  // Modal state
  isEditModalOpen = false;
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  regeneratePrompt = '';

  // Dropdown state
  openDropdownId: string | null = null;

  // Data
  selectedPersona: PersonaData | null = null;
  personas: PersonaData[] = [];
  private subscription = new Subscription();

  // Time period options
  timePeriodOptions: { name: string; value: string }[] = [
    { name: 'Quarter wise', value: 'quarter-wise' },
    { name: 'Month wise', value: 'month-wise' },
    { name: 'Day wise', value: 'day-wise' },
  ];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private personaDataService: PersonaDataService
  ) {}

  ngOnInit(): void {
    // Subscribe to personas data
    this.subscription.add(
      this.personaDataService.personas$.subscribe(personas => {
        this.personas = personas;
      })
    );

    // Subscribe to selected persona
    this.subscription.add(
      this.personaDataService.selectedPersona$.subscribe(persona => {
        this.selectedPersona = persona;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  // --- Dropdown Methods ---
  toggleDropdown(cardId: string): void {
    this.openDropdownId = this.openDropdownId === cardId ? null : cardId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  isDropdownOpen(cardId: string): boolean {
    return this.openDropdownId === cardId;
  }

  // --- Modal Methods ---
  openEditModal(cardType: PersonaCard['type'] | 'profile', title: string): void {
    if (!this.selectedPersona) return;

    this.selectedCardForEdit = {
      id: `${this.selectedPersona.id}-${cardType}`,
      title,
      type: cardType,
      data: cardType === 'profile' ? this.selectedPersona : (this.selectedPersona as any)[cardType]
    };

    // Handle different data types
    if (cardType === 'profile') {
      // For profile, we need to create an object with all profile-related fields
      this.editData = {
        name: this.selectedPersona.name || '',
        role: this.selectedPersona.role || '',
        age: this.selectedPersona.age || '',
        education: this.selectedPersona.education || '',
        status: this.selectedPersona.status || '',
        location: this.selectedPersona.location || '',
        techLiteracy: this.selectedPersona.techLiteracy || '',
        quote: this.selectedPersona.quote || '',
        avatar: this.selectedPersona.avatar || '',
        personality: this.selectedPersona.personality ? [...this.selectedPersona.personality] : []
      };
    } else if ((cardType as string) !== 'profile' && Array.isArray((this.selectedPersona as any)[cardType])) {
      // For arrays, create a deep copy
      this.editData = [...(this.selectedPersona as any)[cardType]];
    } else if ((cardType as string) !== 'profile') {
      // For other types, create a copy
      this.editData = (this.selectedPersona as any)[cardType];
    }

    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit || !this.selectedPersona) return;

    if (this.selectedCardForEdit.type === 'profile') {
      // For profile, update multiple fields
      this.personaDataService.updatePersonaProfile(this.selectedPersona.id, this.editData);
    } else {
      // For other card types, update the specific property
      this.personaDataService.updatePersonaCard(
        this.selectedPersona.id,
        this.selectedCardForEdit.type,
        this.editData
      );
    }

    this.closeEditModal();
  }

  // --- Device Toggle Method ---
  toggleDevice(deviceType: string): void {
    if (!Array.isArray(this.editData)) {
      this.editData = [];
    }

    const index = this.editData.indexOf(deviceType);
    if (index > -1) {
      // Remove device if it exists
      this.editData.splice(index, 1);
    } else {
      // Add device if it doesn't exist
      this.editData.push(deviceType);
    }
  }

  // --- Regenerate Method ---
  onRegenerate(): void {
    if (!this.regeneratePrompt.trim()) {
      console.log('Regenerate prompt is empty.');
      return;
    }

    console.log('Regenerating with prompt:', this.regeneratePrompt);
    // Here you would typically call your AI service to regenerate content
    // For now, we'll just log the prompt
    this.regeneratePrompt = '';
  }

  // --- Navigation Methods ---
  togglePersonaSelector(): void {
    this.isPersonaSelectorOpen = !this.isPersonaSelectorOpen;
  }

  selectPersona(personaId: string): void {
    this.personaDataService.setSelectedPersona(personaId);
    this.isPersonaSelectorOpen = false;
  }

  goBack(): void {
    this.router.navigate(['../persona'], { relativeTo: this.route });
  }

  // --- Utility Methods ---
  getCardTitle(type: PersonaCard['type'] | 'profile'): string {
    const titles = {
      painPoints: 'Pain Points',
      goals: 'Goals',
      motivation: 'Motivation',
      expectations: 'Expectations',
      skills: 'Skills',
      devices: 'Devices',
      profile: 'Profile'
    };
    return titles[type] || type;
  }

  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  addArrayItem(): void {
    if (!Array.isArray(this.editData)) {
      this.editData = [];
    }

    if (this.selectedCardForEdit?.type === 'skills') {
      this.editData.push({ name: '', level: 50 });
    } else if (this.selectedCardForEdit?.type === 'devices') {
      // For devices, we don't add items this way since it's handled by checkboxes
      return;
    } else {
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    if (Array.isArray(this.editData) && index >= 0 && index < this.editData.length) {
      this.editData.splice(index, 1);
    }
  }

  trackByIndex(index: number): number {
    return index;
  }

  getProgressBarClass(index: number): string {
    const classes = ['bg-primary', 'bg-info', 'bg-success'];
    return classes[index % classes.length];
  }

  // --- Helper Methods for Edit Modal ---
  isProfileEdit(): boolean {
    return this.selectedCardForEdit?.type === 'profile';
  }

  isSkillsEdit(): boolean {
    return this.selectedCardForEdit?.type === 'skills';
  }

  isDevicesEdit(): boolean {
    return this.selectedCardForEdit?.type === 'devices';
  }

  isArrayEdit(): boolean {
    return this.isArrayData(this.editData) && 
           !this.isSkillsEdit() && 
           !this.isDevicesEdit() && 
           !this.isProfileEdit();
  }

  // --- Validation Methods ---
  isFormValid(): boolean {
    if (!this.selectedCardForEdit) return false;

    switch (this.selectedCardForEdit.type) {
      case 'profile':
        return this.editData.name?.trim() && this.editData.role?.trim();
      case 'skills':
        return this.editData.every((skill: any) => skill.name?.trim() && skill.level >= 0 && skill.level <= 100);
      case 'devices':
        return Array.isArray(this.editData) && this.editData.length > 0;
      default:
        return Array.isArray(this.editData) ? 
               this.editData.every((item: string) => item?.trim()) : 
               true;
    }
  }
}