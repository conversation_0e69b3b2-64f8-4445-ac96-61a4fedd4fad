import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { PersonaDataService, PersonaData, PersonaCard } from '../../services/persona-data.service';
import { HeadingComponent, IconsComponent, InputComponent, ButtonComponent, DropdownComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-persona-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
    ButtonComponent,
    DropdownComponent
  ],
  templateUrl: './persona-details.component.html',
  styleUrls: ['./persona-details.component.scss']
})
export class PersonaDetailsComponent implements OnInit, OnDestroy {
  // Icons
  PencilEditIcon: string = '/icons/pencil-edit.svg';
  MobileIcon: string = '/mobile-icon/mobile-icon-light.svg';
  LaptopIcon: string = '/web-icon/web-icon-light.svg';
  colon: string = '/svgs/colon.svg';
  InlargeIcon: string = '/svgs/inlarge-icon.svg';
  threeDotsIcon: string = '/icons/three-dot.svg';

  // State
  public isPersonaSelectorOpen = false;

  // Modal state
  isEditModalOpen = false;
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  regeneratePrompt = '';

  // Dropdown state
  openDropdownId: string | null = null;

  // Data
  selectedPersona: PersonaData | null = null;
  personas: PersonaData[] = [];
  private subscription = new Subscription();

  // Time period options
  timePeriodOptions: { name: string; value: string }[] = [
    { name: 'Quarter wise', value: 'quarter-wise' },
    { name: 'Month wise', value: 'month-wise' },
    { name: 'Day wise', value: 'day-wise' },
  ];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private personaDataService: PersonaDataService
  ) {}

  ngOnInit(): void {
    // Subscribe to personas data
    this.subscription.add(
      this.personaDataService.personas$.subscribe(personas => {
        this.personas = personas;
      })
    );

    // Subscribe to selected persona
    this.subscription.add(
      this.personaDataService.selectedPersona$.subscribe(persona => {
        this.selectedPersona = persona;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  // --- Dropdown Methods ---
  toggleDropdown(cardId: string): void {
    this.openDropdownId = this.openDropdownId === cardId ? null : cardId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  isDropdownOpen(cardId: string): boolean {
    return this.openDropdownId === cardId;
  }

  // --- Modal Methods ---
  openEditModal(cardType: PersonaCard['type'], title: string): void {
    if (!this.selectedPersona) return;

    this.selectedCardForEdit = {
      id: `${this.selectedPersona.id}-${cardType}`,
      title,
      type: cardType,
      data: this.selectedPersona[cardType]
    };

    this.editData = Array.isArray(this.selectedPersona[cardType])
      ? [...this.selectedPersona[cardType]]
      : this.selectedPersona[cardType];

    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit || !this.selectedPersona) return;

    this.personaDataService.updatePersonaCard(
      this.selectedPersona.id,
      this.selectedCardForEdit.type,
      this.editData
    );

    this.closeEditModal();
  }

  // --- Regenerate Method ---
  onRegenerate(): void {
    if (!this.regeneratePrompt.trim()) {
      console.log('Regenerate prompt is empty.');
      return;
    }

    console.log('Regenerating with prompt:', this.regeneratePrompt);
    this.regeneratePrompt = '';
  }

  // --- Navigation Methods ---
  togglePersonaSelector(): void {
    this.isPersonaSelectorOpen = !this.isPersonaSelectorOpen;
  }

  selectPersona(personaId: string): void {
    this.personaDataService.setSelectedPersona(personaId);
    this.isPersonaSelectorOpen = false;
  }

  goBack(): void {
    this.router.navigate(['../persona'], { relativeTo: this.route });
  }

  // --- Utility Methods ---
  getCardTitle(type: PersonaCard['type']): string {
    const titles = {
      painPoints: 'Pain Points',
      goals: 'Goals',
      motivation: 'Motivation',
      expectations: 'Expectations',
      skills: 'Skills',
      devices: 'Devices'
    };
    return titles[type];
  }

  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  addArrayItem(): void {
    if (this.selectedCardForEdit?.type === 'skills') {
      this.editData.push({ name: '', level: 50 });
    } else {
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    this.editData.splice(index, 1);
  }

  trackByIndex(index: number): number {
    return index;
  }

  getProgressBarClass(index: number): string {
    const classes = ['bg-primary', 'bg-info', 'bg-success'];
    return classes[index % classes.length];
  }
}