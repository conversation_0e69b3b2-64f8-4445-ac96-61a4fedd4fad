<div class="roadmap-container">
  <!-- <h1>Project Timeline - Nap App Development</h1> -->
  <div class="roadmap-nav">
    <!-- Left Section: Dropdown + Date Picker -->
    <div class="left-section d-flex align-items-center gap-3">
      <div class="timeline-dropdown">
        <awe-dropdown
          class="d-flex align-self-start"
          selectedValue="Quarter wise"
          [options]="[
            { name: 'Quarter wise', value: 'quarter-wise' },
            { name: 'Month wise', value: 'month-wise' },
            { name: 'Day wise', value: 'day-wise' },
          ]"
          animation="rotateX"
          theme="light"
        ></awe-dropdown>
      </div>

      <div class="date-selection">
        <awe-datepicker
          class="d-flex align-self-start"
          [range]="true"
          [size]="'large'"
          (dateRangeSelected)="onRangeSelected($event)"
        ></awe-datepicker>
      </div>
      <div class="nav-btn d-flex align-items-center gap-3">
        <button class="nav-arrow p-4">
          <awe-icons class="next-icon" iconName="awe_chevron_left"></awe-icons>
        </button>
        <button class="nav-arrow p-4">
          <awe-icons class="next-icon" iconName="awe_chevron_right"></awe-icons>
        </button>
      </div>
    </div>

    <!-- Center Section: Navigation Arrows -->

    <!-- Right Section: View Toggle + Add Task -->
    <div class="right-container d-flex align-items-center gap-3 m-4">
      <div class="roadmap-view">
        <div class="toggle-view d-flex align-items-center gap-4 px-4 py-2">
          <button
            class="btn-toggle"
            [class.active]="activeView === 'timeline'"
            (click)="setActiveView('timeline')"
            title="Timeline View"
          >
            <awe-icons iconName="awe_performance_ metrics"></awe-icons>
          </button>

          <button
            class="btn-toggle"
            [class.active]="activeView === 'card'"
            (click)="setActiveView('card')"
            title="Card View"
          >
            <awe-icons iconName="awe_dock_to_right"></awe-icons>
          </button>
        </div>
      </div>

      <div class="add-task-btn">
        <button class="" (click)="addNewTask()">New Task +</button>
      </div>
    </div>
  </div>

  <!-- Timeline View - Gantt Chart -->
  <app-gantt-chart
    *ngIf="activeView === 'timeline'"
    [tasks]="projectTasks"
    [year]="2025"
  >
  </app-gantt-chart>

  <!-- Card View - Roadmap Cards -->
  <app-product-roadmap-card-view *ngIf="activeView === 'card'">
  </app-product-roadmap-card-view>
</div>

<!-- Edit/Add Task Modal -->
<awe-modal
  *ngIf="isEditModalOpen"
  [isOpen]="isEditModalOpen"
  (closeModal)="closeEditModal()"
  class="edit-modal"
>
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s2" type="bold" class="modal-title mb-0">
      {{ isAddingNewTask ? "Add New Roadmap Task" : "Edit Roadmap Task" }}
    </awe-heading>
  </div>

  <div awe-modal-body class="edit-modal-body">

    <div class="col-md-12 inp-container">
      <div class="label">
        <label for="name">Title:</label>
      </div>
      <div class="input-wrapper">
        <awe-input
          id="taskTitle"
          type="text"
          variant="fluid"
          label="Name:"
          [(ngModel)]="editableTaskTitle"
          placeholder="Enter name"
          class="w-100"
        ></awe-input>
      </div>
    </div>

    <!-- Task Description -->
    <div class="form-group mb-3">
      <label for="taskDescription" class="form-label">Description:</label>
      <textarea
        id="taskDescription"
        class="form-control"
        rows="3"
        [(ngModel)]="editableTaskDescription"
        placeholder="Enter roadmap task description"
      ></textarea>
    </div>

    <!-- Priority -->
    <div class="form-group mb-3">
      <label for="taskPriority" class="form-label">Priority:</label>
      <select
        id="taskPriority"
        class="form-control"
        [(ngModel)]="editableTaskPriority"
      >
        <option value="low">Low</option>
        <option value="medium">Medium</option>
        <option value="high">High</option>
      </select>
    </div>

    <!-- Start Date -->
    <div class="form-group mb-3">
      <label for="taskStartDate" class="form-label">Start Date:</label>
      <input
        id="taskStartDate"
        type="date"
        class="form-control"
        [(ngModel)]="editableTaskStartDate"
      />
    </div>

    <!-- End Date -->
    <div class="form-group mb-3">
      <label for="taskEndDate" class="form-label">End Date:</label>
      <input
        id="taskEndDate"
        type="date"
        class="form-control"
        [(ngModel)]="editableTaskEndDate"
      />
    </div>

    <!-- Regenerate Section -->
    <div class="form-group mb-3">
      <label for="regeneratePrompt" class="form-label">Regenerate:</label>
      <textarea
        id="regeneratePrompt"
        class="form-control regenerate-textarea"
        rows="3"
        [(ngModel)]="regeneratePrompt"
        placeholder="What would you like to change?"
      ></textarea>
    </div>
  </div>

  <div
    awe-modal-footer
    class="edit-modal-footer d-flex justify-content-between"
  >
    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
      Cancel
    </button>
    <button type="button" class="btn btn-primary" (click)="updateTask()">
      {{ isAddingNewTask ? "Add Task" : "Update" }}
    </button>
  </div>
</awe-modal>
