import { Component, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSliderModule } from '@angular/material/slider';
import {
  ButtonComponent,
  HeadingComponent,
  IconsComponent,
  InputComponent,
} from '@awe/play-comp-library'; // Adjust path if needed

export interface Skill {
  name: string;
  level: number;
}

@Component({
  selector: 'app-edit-skills-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatSliderModule, // Import for the slider
    HeadingComponent,
    InputComponent,
    ButtonComponent,
    IconsComponent,
  ],
  templateUrl: './edit-skills-dialog.component.html',
  styleUrls: ['./edit-skills-dialog.component.scss'],
})
export class EditSkillsDialogComponent {
  editableSkills: Skill[];
  regeneratePrompt: string = '';
  isLoading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<EditSkillsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: Skill[],
    private cdRef: ChangeDetectorRef
  ) {
    // Create a deep copy to avoid mutating the original data until save
    this.editableSkills = data.map(skill => ({ ...skill }));
  }

  onUpdate(): void {
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
      this.dialogRef.close(this.editableSkills);
    }, 1500);
  }

  addSkill(): void {
    this.editableSkills.push({ name: 'New Skill', level: 50 });
    this.cdRef.detectChanges();
    setTimeout(() => {
      const inputs = document.querySelectorAll('.skill-name-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      lastInput?.focus();
    });
  }

  removeSkill(index: number): void {
    this.editableSkills.splice(index, 1);
  }

  trackByFn(index: number): any {
    return index;
  }
}