// General Dialog Styles
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  .dialog-title { font-size: 1.25rem; font-weight: 600; margin: 0; }
  .close-button { color: #6c757d; }
}

.dialog-content { padding: 1.5rem; }
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
}

// Button Styles
.btn-cancel {
  border-color: #dee2e6;
  color: #495057;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
}
.btn-update {
  background-color: #7b4cff;
  color: white;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
}

// Skills Section
.skills-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.skill-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  align-items: center;
  gap: 1rem;
}

.skill-name-input {
  border: none;
  font-weight: 500;
  padding: 0;
  &:focus { outline: none; }
}

.add-skill-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-color: #7b4cff;
  color: #7b4cff;
  margin-bottom: 1.5rem;
}

.remove-skill-btn { color: #dc3545; }

// Material Slider Override
:host ::ng-deep .skill-slider {
  .mdc-slider__track--active_fill {
    border-color: #7b4cff !important;
  }
  .mdc-slider__thumb-knob {
    border-color: #7b4cff !important;
    background-color: #7b4cff !important;
  }
}

// Regenerate Section
.regenerate-section {
  margin-top: 1rem;
  .form-label { font-weight: 600; margin-bottom: 0.5rem; }
  .regenerate-input-wrapper {
    position: relative;
    textarea {
      border: 1px solid #f472b6;
      border-radius: 8px;
      padding: 0.75rem;
      padding-right: 40px;
      min-height: 80px;
      &:focus {
        outline: none;
        border-color: #ec4899;
        box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.15);
      }
    }
  }
  .regenerate-send-btn {
    position: absolute;
    right: 12px;
    bottom: 12px;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #be185d;
    cursor: pointer;
  }
}