<div mat-dialog-title class="dialog-header">
  <h2 class="dialog-title">Edit Skillset</h2>
  <button mat-icon-button (click)="dialogRef.close()" class="close-button" aria-label="Close dialog">
    <i class="bi bi-x-lg"></i>
  </button>
</div>

<div mat-dialog-content class="dialog-content">
  <!-- Editable Skills -->
  <div class="skills-list">
    <div *ngFor="let skill of editableSkills; let i = index; trackBy: trackByFn" class="skill-item">
      <input type="text" class="form-control skill-name-input" [(ngModel)]="skill.name" />
      <mat-slider class="skill-slider" min="0" max="100" step="1">
        <input matSliderThumb [(ngModel)]="skill.level">
      </mat-slider>
      <button mat-icon-button (click)="removeSkill(i)" class="remove-skill-btn">
        <i class="bi bi-trash"></i>
      </button>
    </div>
  </div>

  <!-- Add New Skill Button -->
  <button mat-stroked-button (click)="addSkill()" class="add-skill-btn">
    <i class="bi bi-plus"></i>
  </button>

  <!-- Regenerate Section -->
  <div class="regenerate-section">
    <label for="regenerate" class="form-label">Regenerate</label>
    <div class="regenerate-input-wrapper">
      <textarea id="regenerate" class="form-control" name="regenerate" [(ngModel)]="regeneratePrompt"
        placeholder="What would you like to change?"></textarea>
      <button class="regenerate-send-btn">➤</button>
    </div>
  </div>
</div>

<div mat-dialog-actions class="dialog-footer">
  <button mat-stroked-button (click)="dialogRef.close()" class="btn-cancel">Cancel</button>
  <button mat-flat-button color="primary" (click)="onUpdate()" class="btn-update">
    <span *ngIf="!isLoading">Update</span>
    <div *ngIf="isLoading" class="spinner-border spinner-border-sm" role="status"></div>
  </button>
</div>