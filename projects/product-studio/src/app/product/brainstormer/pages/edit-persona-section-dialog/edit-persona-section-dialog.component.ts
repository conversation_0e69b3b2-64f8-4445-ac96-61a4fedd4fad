import { Component, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import {
  BodyTextComponent,
  ButtonComponent,
  HeadingComponent,
  IconsComponent,
  InputComponent,
} from '@awe/play-comp-library'; // Adjust path if needed

export interface DialogData {
  title: string;
  data: string[];
}

@Component({
  selector: 'app-edit-persona-section-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HeadingComponent,
    BodyTextComponent,
    InputComponent,
    ButtonComponent,
    IconsComponent,
  ],
  templateUrl: './edit-persona-section-dialog.component.html',
  styleUrls: ['./edit-persona-section-dialog.component.scss'],
})
export class EditPersonaSectionDialogComponent {
  editableData: string[];
  title: string;
  regeneratePrompt: string = '';
  isLoading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<EditPersonaSectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private cdRef: ChangeDetectorRef
  ) {
    // Make a copy of the data to avoid mutating the original until save
    this.title = data.title;
    this.editableData = [...data.data];
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onUpdate(): void {
    this.isLoading = true;
    // In a real app, you might have an API call here.
    // We'll simulate a delay.
    setTimeout(() => {
      this.isLoading = false;
      // Close the dialog and pass the updated data back
      this.dialogRef.close(this.editableData);
    }, 1500);
  }

  addItem(): void {
    this.editableData.push('');
    this.cdRef.detectChanges(); // Ensure ngFor updates before focusing
    setTimeout(() => {
      const inputs = document.querySelectorAll('.editable-item-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      lastInput?.focus();
    });
  }

  removeItem(index: number): void {
    this.editableData.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] to prevent focus issues
  trackByFn(index: number, item: any): any {
    return index;
  }
}