<div class="edit-modal-header" mat-dialog-title>
  <awe-heading variant="s1" type="bold">Edit {{ title }}</awe-heading>
</div>

<div class="edit-modal-body" mat-dialog-content>
  <!-- Editable Data Items -->
  <div *ngFor="let item of editableData; let i = index; trackBy: trackByFn"
    class="editable-data-item">
    <input
      type="text"
      class="form-control editable-item-input"
      [(ngModel)]="editableData[i]"
      placeholder="Enter data point"
    />
    <awe-icons
      iconName="awe_trash"
      class="icon-button"
      iconColor="danger"
      (click)="removeItem(i)"
      title="Remove item"
    ></awe-icons>
  </div>

  <!-- Add New Data Item Button -->
  <div (click)="addItem()" class="add-new-data-btn">
    <span>Add New</span>
    <awe-icons iconName="awe_plus" color="primary"></awe-icons>
  </div>

  <!-- Regenerate Section -->
  <div class="regenerate-section mt-4">
    <awe-input
      id="regeneratePrompt"
      label="Regenerate"
      variant="fluid"
      [icons]="['awe_send']"
      iconColor="action"
      [(ngModel)]="regeneratePrompt"
      placeholder="Type your prompt to regenerate content..."
    >
    </awe-input>
  </div>
</div>

<div class="edit-modal-footer" mat-dialog-actions>
  <awe-button
    type="button"
    (userClick)="onUpdate()"
    label="Update"
    variant="primary"
    class="w-100"
    width="100%"
    height="50px"
    gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
    hoverEffect="slide-bg"
    [loading]="isLoading"
    loadingType="spinner"
  >
  </awe-button>
</div>