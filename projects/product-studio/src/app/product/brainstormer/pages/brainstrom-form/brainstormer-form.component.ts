import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AweCardComponent } from "../../components/awe-card/awe-card.component";

@Component({
  selector: 'app-brainstormer-form',
  standalone: true,
  imports: [CommonModule, FormsModule, AweCardComponent],
  templateUrl: './brainstormer-form.component.html',
  styleUrls: ['./brainstormer-form.component.scss'],
})
export class BrainstormerFormComponent {
  sparkleIcon: string = '/icons/sparkle-icon.svg';
  addIcon: string = '/icons/add-icon.svg';
  projectName = '';
  industry = 'Banking';

  industries = [
    'Banking', 'Financial Services', 'Healthcare', 'Lifesciences', 
    'Manufacturing', 'Mortgage', 'Retail', 'HI-Tech', 
    'Travel and Hospitality', 'Education', 'Other'
  ];

  userGroups = [
    { label: 'Business Users', selected: true },
    { label: 'End Consumers', selected: true },
    { label: 'Technical Users', selected: false },
    { label: 'Creative Users', selected: false },
  ];

  toggleGroup(index: number) {
    this.userGroups[index].selected = !this.userGroups[index].selected;
  }

  onSubmit() {
    const selectedUsers = this.userGroups
      .filter(group => group.selected)
      .map(group => group.label);
    
    console.log('Form Submitted:', {
      project: this.projectName,
      industry: this.industry,
      users: selectedUsers
    });
  }
}