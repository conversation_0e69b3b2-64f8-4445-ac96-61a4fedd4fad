// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap");
// Section Headers


.section-header {
  display: flex;
  // align-items: center;
  justify-content: space-between;
  height: auto;
  background: var(--roadmap-card-header-bg) !important;
  color: var(--roadmap-card-header-title-color);
  border-radius: 16px 16px 0px 0px;
  // border-bottom: 3px solid #042f5a;

  .bullet-pt {
    width: 10px;
    height: 10px;
    // background-color: #5030e5;
    align-items: center;
    border-radius: 50%;
    margin-right: 10px;
  }

  .section-title {
    display: flex;
    align-items: center;
    align-self: center;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 1px;
    gap: 8px;

    .task-count-badge {
      background-color: #E5E5E5;
      color: #666666;
      border-radius: 12px;
      // padding: 4px 8px;
      font-size: 14px;
      font-weight: 500;
      min-width: 24px;
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--roadmap-card-header-action-bg);
    border-radius: 16px;
    color: var(--roadmap-card-header-action-color);
    padding: 16px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
  }
}
.roadmap-card-item-card {
  --awe-card-border: #e9ecef;
  --awe-card-box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
  --awe-card-border-radius: 0.5rem;
  --awe-card-padding-base: 1rem;
  display: flex;
  flex-direction: column;
  font-family: "Mulish", sans-serif;
  font-weight: 600;
  color: var(--roadmap-card-color);
  font-size: 1rem;
  border-radius: var(--roadmap-card-border-redius);
  border: 1px solid var(--awe-card-border);
  background: var(--roadmap-card-bg);
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
  cursor: grab !important;

  // Header content layout
  [awe-card-header-content] {
    .roadmap-card-tag {
      background-color: var(--roadmap-card-tag-bg);
      color: var(--roadmap-card-tag-color);
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      white-space: nowrap;
      max-width: fit-content;
    }
  }

  .roadmap-card-description {
    color: var(--roadmap-card-desc-color);
    line-height: 1.5;
  }
  
  &:hover {
    --awe-card-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

:host ::ng-deep .roadmap-card-title {
  color: var(--roadmap-card-title);
  font-size: 16px;
  line-height: 1.3;
  margin: 0;
}

// Three-dot icon positioning
.three-dot-icon {
  cursor: pointer !important;
  // padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

:host ::ng-deep .roadmap-card-title {
  color: var(--roadmap-card-title);
  gap: 8px;
}

// Custom dropdown styling
// Improved dropdown styling
.dropdown-arrow {
  .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border: 1px solid #dee2e6;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: flex;
      width: 100%;
      padding: 0.5rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      border: 0;
      transition: background-color 0.15s ease-in-out;
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }

      &.border-buttom {
        border-bottom: 1px solid #e9ecef;
      }
    }
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}

.border-buttom {
  border-bottom: 1px solid #303233;
}
.three-dot-icon {
  // margin: 1.25rem;
  cursor: pointer !important;
}

.add-more-section {
  .add-more-btn {
    background-color: var(--roadmap-card-add-card-btn-bg);
    border: 1px solid var(--roadmap-card-add-card-btn-border);
    border-radius: 12px;
    color: var(--roadmap-card-add-card-btn-color);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;

    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba(108, 117, 125, 0.1);
  border: 2px dashed #6c757d;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.roadmap-card-list-dropzone.cdk-drop-list-dragging
  awe-card.roadmap-card-item-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}

.edit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.roadmap-card-tag {
  background-color: var(--roadmap-card-tag-bg);
  color: var(--roadmap-card-tag-color);
  border-radius: 4px;
}

.roadmap-card-list-dropzone {
  cursor: grab !important;
  &:active {
    cursor: grabbing !important;
  }
}

.roadmap-card-main {
  background-color: var(--roadmap-card-bg);
  min-height: 640px;
  border-radius: 16px;
  border: 0.6px solid #d4d4d4;
  background: #fff;

  /* where are you? */
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

// STYLES FOR THE MODAL CONTENT
.edit-modal-header {
  .modal-title {
    font-weight: 600;
  }
}

.edit-modal-body {
  .roadmap-card-title {
    font-weight: 500;
    color: #555;
  }

  .editable-tag-item {
    input.form-control-sm {
      border-radius: 0.5rem;
    }
    .btn-outline-danger {
      border-radius: 0.5rem;
      padding: 0.25rem 0.5rem;
      line-height: 1;
    }
  }

  .add-new-tag-btn {
    border-radius: 0.5rem;
    border-style: dashed;
    color: #6c757d;
    border-color: #ced4da;
    &:hover {
      background-color: #e9ecef;
    }
  }

  .regenerate-section {
    .form-label {
      font-size: 0.9rem;
    }
    .input-group {
      input.form-control-sm,
      button.btn-sm {
        border-radius: 0.5rem;
      }
      .btn-sm {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      input.form-control-sm {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }
}
