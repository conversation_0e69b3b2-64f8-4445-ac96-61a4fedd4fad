import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ButtonComponent, HeadingComponent, IconsComponent } from '@awe/play-comp-library';

interface Device {
  id: 'mobile' | 'laptop' | 'desktop' | 'tablet';
  icon: string; // Bootstrap icon class name
}

@Component({
  selector: 'app-edit-devices-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule, HeadingComponent, ButtonComponent, IconsComponent],
  templateUrl: './edit-devices-dialog.component.html',
  styleUrls: ['./edit-devices-dialog.component.scss'],
})
export class EditDevicesDialogComponent {
  allDevices: Device[] = [
    { id: 'mobile', icon: 'bi-phone' },
    { id: 'laptop', icon: 'bi-laptop' },
    { id: 'desktop', icon: 'bi-display' },
    { id: 'tablet', icon: 'bi-tablet' },
  ];
  selectedDevices: Set<string>;
  isLoading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<EditDevicesDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: string[]
  ) {
    this.selectedDevices = new Set(data);
  }

  toggleDevice(deviceId: string): void {
    if (this.selectedDevices.has(deviceId)) {
      this.selectedDevices.delete(deviceId);
    } else {
      this.selectedDevices.add(deviceId);
    }
  }

  isSelected(deviceId: string): boolean {
    return this.selectedDevices.has(deviceId);
  }

  onUpdate(): void {
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
      this.dialogRef.close(Array.from(this.selectedDevices));
    }, 1000);
  }
}