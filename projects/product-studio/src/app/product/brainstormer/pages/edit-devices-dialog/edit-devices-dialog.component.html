<div mat-dialog-title class="dialog-header">
  <h2 class="dialog-title">Edit Devices</h2>
  <button mat-icon-button (click)="dialogRef.close()" class="close-button" aria-label="Close dialog">
    <i class="bi bi-x-lg"></i>
  </button>
</div>

<div mat-dialog-content class="dialog-content">
  <div class="devices-grid">
    <div *ngFor="let device of allDevices"
         class="device-item"
         [class.selected]="isSelected(device.id)"
         (click)="toggleDevice(device.id)">
      <i class="bi {{device.icon}}"></i>
    </div>
  </div>
</div>

<div mat-dialog-actions class="dialog-footer">
  <button mat-stroked-button (click)="dialogRef.close()" class="btn-cancel">Cancel</button>
  <button mat-flat-button color="primary" (click)="onUpdate()" class="btn-update">
    <span *ngIf="!isLoading">Update</span>
    <div *ngIf="isLoading" class="spinner-border spinner-border-sm" role="status"></div>
  </button>
</div>