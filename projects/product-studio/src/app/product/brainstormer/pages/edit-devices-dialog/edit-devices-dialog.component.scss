// General Dialog Styles (can be shared or copied from skills dialog)
.dialog-header {
  display: flex; justify-content: space-between; align-items: center;
  padding: 1.5rem; border-bottom: 1px solid #e9ecef;
  .dialog-title { font-size: 1.25rem; font-weight: 600; margin: 0; }
  .close-button { color: #6c757d; }
}
.dialog-content { padding: 3rem 1.5rem; } // More vertical padding
.dialog-footer {
  display: flex; justify-content: flex-end; gap: 0.75rem;
  padding: 1.5rem; border-top: 1px solid #e9ecef;
}
.btn-cancel {
  border-color: #dee2e6; color: #495057; border-radius: 20px; padding: 0.5rem 1.5rem;
}
.btn-update {
  background-color: #7b4cff; color: white; border-radius: 20px; padding: 0.5rem 1.5rem;
}

// Devices Section
.devices-grid {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.device-item {
  width: 80px;
  height: 80px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: #adb5bd; // Deselected color

  i { font-size: 2.5rem; }

  &:hover {
    border-color: #7b4cff;
    color: #7b4cff;
  }

  &.selected {
    border-color: #7b4cff;
    background-color: #f3f0ff;
    color: #7b4cff; // Selected color
  }
}