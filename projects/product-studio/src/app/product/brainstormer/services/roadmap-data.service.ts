import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Priority = 'low' | 'medium' | 'high';

export interface RoadmapTask {
  id: string;
  task: string; // Used in card view
  name?: string; // Used in timeline view (same as task)
  description: string;
  priority: Priority;
  startDate: Date;
  endDate: Date;
  quarter: number;
  color?: string; // Used in timeline view
}

export interface QuarterSection {
  id: string;
  title: string; // 'Quarter 1', 'Quarter 2', etc.
  qurterColor: string;
  subtitle: string; // Date range
  tasks: RoadmapTask[];
}

// For timeline view compatibility
export interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  color: string;
  quarter: number;
}

@Injectable({
  providedIn: 'root'
})
export class RoadmapDataService {
  private tasksSubject = new BehaviorSubject<RoadmapTask[]>([
    {
      id: '1',
      task: 'Smart Nap Timer',
      description: 'Intelligent timer system that adapts to user sleep patterns and preferences.',
      priority: 'high',
      startDate: new Date(2025, 0, 1), // Jan 1
      endDate: new Date(2025, 2, 1), // Mar 1
      quarter: 1,
      color: '#10b981',
    },
    {
      id: '2',
      task: 'Nap Stats Dashboard',
      description: 'Comprehensive analytics dashboard showing nap patterns, quality metrics, and insights.',
      priority: 'medium',
      startDate: new Date(2025, 3, 1), // Apr 1
      endDate: new Date(2025, 5, 30), // Jun 30
      quarter: 2,
      color: '#f59e0b',
    },
    {
      id: '3',
      task: 'Gentle Wake System',
      description: 'Advanced wake-up system using gradual light, sound, and vibration patterns.',
      priority: 'high',
      startDate: new Date(2025, 4, 1), // May 1
      endDate: new Date(2025, 6, 30), // Jul 30
      quarter: 2,
      color: '#10b981',
    },
    {
      id: '4',
      task: 'Mood-to-Nap Match',
      description: 'AI-powered system that recommends nap types based on current mood and energy levels.',
      priority: 'medium',
      startDate: new Date(2025, 6, 15), // Jul 15
      endDate: new Date(2025, 8, 30), // Sep 30
      quarter: 3,
      color: '#8b5cf6',
    },
    {
      id: '5',
      task: 'Sleep Environment Control',
      description: 'Smart integration with IoT devices to optimize room temperature, lighting, and sound.',
      priority: 'low',
      startDate: new Date(2025, 9, 1), // Oct 1
      endDate: new Date(2025, 11, 15), // Dec 15
      quarter: 4,
      color: '#06b6d4',
    },
    {
      id: '6',
      task: 'Social Nap Features',
      description: 'Community features for sharing nap experiences and connecting with other users.',
      priority: 'low',
      startDate: new Date(2025, 9, 15), // Oct 15
      endDate: new Date(2025, 11, 30), // Dec 30
      quarter: 4,
      color: '#f97316',
    },
  ]);

  private quartersSubject = new BehaviorSubject<QuarterSection[]>([]);

  constructor() {
    this.initializeQuarters();
  }

  // Observable streams
  get tasks$(): Observable<RoadmapTask[]> {
    return this.tasksSubject.asObservable();
  }

  get quarters$(): Observable<QuarterSection[]> {
    return this.quartersSubject.asObservable();
  }

  // Get current values
  get tasks(): RoadmapTask[] {
    return this.tasksSubject.value;
  }

  get quarters(): QuarterSection[] {
    return this.quartersSubject.value;
  }

  // Convert to GanttTask format for timeline view
  getGanttTasks(): GanttTask[] {
    return this.tasks.map(task => ({
      id: task.id,
      name: task.task,
      startDate: task.startDate,
      endDate: task.endDate,
      color: task.color || this.getDefaultColor(task.priority),
      quarter: task.quarter,
    }));
  }

  // Add new task
  addTask(task: Omit<RoadmapTask, 'id'>): void {
    const newTask: RoadmapTask = {
      ...task,
      id: `task-${Date.now()}`,
      color: task.color || this.getDefaultColor(task.priority),
    };
    
    const currentTasks = this.tasksSubject.value;
    this.tasksSubject.next([...currentTasks, newTask]);
    this.updateQuarters();
  }

  // Update existing task
  updateTask(taskId: string, updates: Partial<RoadmapTask>): void {
    const currentTasks = this.tasksSubject.value;
    const updatedTasks = currentTasks.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    );
    this.tasksSubject.next(updatedTasks);
    this.updateQuarters();
  }

  // Delete task
  deleteTask(taskId: string): void {
    const currentTasks = this.tasksSubject.value;
    const filteredTasks = currentTasks.filter(task => task.id !== taskId);
    this.tasksSubject.next(filteredTasks);
    this.updateQuarters();
  }

  // Get task by ID
  getTaskById(taskId: string): RoadmapTask | undefined {
    return this.tasks.find(task => task.id === taskId);
  }

  // Private helper methods
  private initializeQuarters(): void {
    const quarters: QuarterSection[] = [
      {
        id: 'q1',
        title: 'Quarter 1',
        qurterColor: '#68B266',
        subtitle: 'Jan - Mar 2025',
        tasks: [],
      },
      {
        id: 'q2',
        title: 'Quarter 2',
        qurterColor: '#D58D49',
        subtitle: 'Apr - Jun 2025',
        tasks: [],
      },
      {
        id: 'q3',
        title: 'Quarter 3',
        qurterColor: '#8b5cf6',
        subtitle: 'Jul - Sep 2025',
        tasks: [],
      },
      {
        id: 'q4',
        title: 'Quarter 4',
        qurterColor: '#06b6d4',
        subtitle: 'Oct - Dec 2025',
        tasks: [],
      },
    ];

    this.quartersSubject.next(quarters);
    this.updateQuarters();
  }

  private updateQuarters(): void {
    const quarters = this.quartersSubject.value.map(quarter => ({
      ...quarter,
      tasks: this.tasks.filter(task => task.quarter === parseInt(quarter.id.replace('q', ''))),
    }));
    
    this.quartersSubject.next(quarters);
  }

  private getDefaultColor(priority: Priority): string {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  }

  // Utility method to get quarter from date
  getQuarterFromDate(date: Date): number {
    const month = date.getMonth();
    if (month >= 0 && month <= 2) return 1; // Jan-Mar
    if (month >= 3 && month <= 5) return 2; // Apr-Jun
    if (month >= 6 && month <= 8) return 3; // Jul-Sep
    return 4; // Oct-Dec
  }

  // Format date for input fields
  formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Parse date from input fields
  parseDateFromInput(dateString: string): Date {
    return new Date(dateString);
  }
}
