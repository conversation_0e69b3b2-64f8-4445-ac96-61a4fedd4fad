import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Skill {
  name: string;
  level: number;
}

export interface PersonaData {
  id: string;
  name: string;
  role: string;
  age: number;
  education: string;
  status: string;
  location: string;
  techLiteracy: string;
  quote: string;
  personality: string[];
  painPoints: string[];
  goals: string[];
  motivation: string[];
  expectations: string[];
  skills: Skill[];
  devices: string[];
  avatar: string;
}

export interface PersonaCard {
  id: string;
  title: string;
  type: 'painPoints' | 'goals' | 'motivation' | 'expectations' | 'skills' | 'devices';
  data: string[] | Skill[] | string[];
  icon?: string;
}

@Injectable({
  providedIn: 'root'
})
export class PersonaDataService {
  private personasSubject = new BehaviorSubject<PersonaData[]>([
    {
      id: '1',
      name: '<PERSON>',
      role: 'Sales Manager',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Mumbai',
      techLiteracy: 'Medium',
      avatar: '/svgs/sales-avatar.svg',
      quote: 'I am used to with online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender'],
      painPoints: ['Faces PIN issues when using ATMs', 'Often forgets passwords for online banking', 'Concerned about online fraud while shopping'],
      goals: ['Fast and easy payments without carrying cash', 'Secure online transactions', 'Budget-friendly banking solutions'],
      motivation: ['Wants a simple, secure, and student-friendly debit card', 'Prefers biometric authentication over remembering PINs', 'Seeks a smooth online shopping experience'],
      expectations: ['Seamless biometric authentication for fast, PIN-free transactions.', 'Secure online payments with fraud protection', 'User-friendly mobile banking for tracking spending.'],
      skills: [{ name: 'skill 1', level: 85 }, { name: 'skill 2', level: 70 }, { name: 'skill 3', level: 60 }],
      devices: ['mobile', 'laptop']
    },
    {
      id: '2',
      name: 'Donald Terry',
      role: 'Designer',
      age: 27,
      education: 'M.Des',
      status: 'Single',
      location: 'Abu Dhabi',
      techLiteracy: 'High',
      avatar: '/svgs/designer-avatar.svg',
      quote: 'I love creating beautiful and functional designs that users enjoy.',
      personality: ['Creative', 'Detail-oriented', 'Collaborative'],
      painPoints: ['Struggles with complex financial interfaces', 'Needs visual feedback for transactions', 'Wants seamless design tools integration'],
      goals: ['Streamlined payment workflows', 'Visual transaction confirmations', 'Creative-friendly banking features'],
      motivation: ['Seeks aesthetically pleasing interfaces', 'Values user experience consistency', 'Wants efficient creative workflow support'],
      expectations: ['Beautiful, intuitive interface design', 'Quick visual feedback on transactions', 'Integration with creative tools and platforms'],
      skills: [{ name: 'UI/UX Design', level: 90 }, { name: 'Creative Software', level: 85 }, { name: 'User Research', level: 75 }],
      devices: ['mobile', 'laptop']
    },
    {
      id: '3',
      name: 'Sarah Johnson',
      role: 'Teacher',
      age: 32,
      education: 'M.Ed',
      status: 'Married',
      location: 'Melbourne',
      techLiteracy: 'Medium',
      avatar: '/svgs/teacher-avatar.svg',
      quote: 'I believe in making learning accessible and engaging for all students.',
      personality: ['Patient', 'Organized', 'Empathetic'],
      painPoints: ['Limited budget for educational tools', 'Difficulty tracking student expenses', 'Need for secure payment methods'],
      goals: ['Affordable educational resources', 'Easy expense tracking', 'Secure online transactions'],
      motivation: ['Wants to provide the best education possible', 'Seeks cost-effective solutions', 'Values security and reliability'],
      expectations: ['Budget-friendly banking options', 'Educational discounts and offers', 'Simple expense categorization'],
      skills: [{ name: 'Teaching', level: 95 }, { name: 'Technology', level: 65 }, { name: 'Communication', level: 90 }],
      devices: ['mobile', 'laptop']
    },
    {
      id: '4',
      name: 'Mike Chen',
      role: 'Developer',
      age: 28,
      education: 'MCA',
      status: 'Single',
      location: 'Qatar',
      techLiteracy: 'High',
      avatar: '/svgs/developer-avatar.svg',
      quote: 'Code is poetry, and I love writing elegant solutions.',
      personality: ['Analytical', 'Detail-oriented', 'Innovative'],
      painPoints: ['Complex banking interfaces', 'Lack of API integration', 'Poor mobile experience'],
      goals: ['Seamless digital banking', 'API access for personal projects', 'Advanced security features'],
      motivation: ['Wants cutting-edge technology', 'Values efficiency and automation', 'Seeks customizable solutions'],
      expectations: ['Modern, intuitive interfaces', 'Developer-friendly features', 'Advanced security options'],
      skills: [{ name: 'Programming', level: 95 }, { name: 'System Design', level: 85 }, { name: 'Problem Solving', level: 90 }],
      devices: ['mobile', 'laptop']
    },
    {
      id: '5',
      name: 'Mike ',
      role: 'CA',
      age: 28,
      education: 'MCA',
      status: 'Single',
      location: 'Qatar',
      techLiteracy: 'High',
      avatar: '/svgs/developer-avatar.svg',
      quote: 'Code is poetry, and I love writing elegant solutions.',
      personality: ['Analytical', 'Detail-oriented', 'Innovative'],
      painPoints: ['Complex banking interfaces', 'Lack of API integration', 'Poor mobile experience'],
      goals: ['Seamless digital banking', 'API access for personal projects', 'Advanced security features'],
      motivation: ['Wants cutting-edge technology', 'Values efficiency and automation', 'Seeks customizable solutions'],
      expectations: ['Modern, intuitive interfaces', 'Developer-friendly features', 'Advanced security options'],
      skills: [{ name: 'Programming', level: 95 }, { name: 'System Design', level: 85 }, { name: 'Problem Solving', level: 90 }],
      devices: ['mobile', 'laptop']
    }
  ]);

  private selectedPersonaSubject = new BehaviorSubject<PersonaData | null>(null);

  personas$ = this.personasSubject.asObservable();
  selectedPersona$ = this.selectedPersonaSubject.asObservable();

  constructor() {
    // Set the first persona as selected by default
    const personas = this.personasSubject.value;
    if (personas.length > 0) {
      this.selectedPersonaSubject.next(personas[0]);
    }
  }

  // Persona CRUD operations
  getPersonas(): PersonaData[] {
    return this.personasSubject.value;
  }

  getPersonaById(id: string): PersonaData | undefined {
    return this.personasSubject.value.find(persona => persona.id === id);
  }

  addPersona(persona: Omit<PersonaData, 'id'>): void {
    const newPersona: PersonaData = {
      ...persona,
      id: this.generateId()
    };
    const currentPersonas = this.personasSubject.value;
    this.personasSubject.next([...currentPersonas, newPersona]);
  }

  updatePersona(personaId: string, updates: Partial<PersonaData>): void {
    const currentPersonas = this.personasSubject.value;
    const updatedPersonas = currentPersonas.map(persona =>
      persona.id === personaId ? { ...persona, ...updates } : persona
    );
    this.personasSubject.next(updatedPersonas);

    // Update selected persona if it's the one being updated
    const selectedPersona = this.selectedPersonaSubject.value;
    if (selectedPersona && selectedPersona.id === personaId) {
      this.selectedPersonaSubject.next({ ...selectedPersona, ...updates });
    }
  }

  deletePersona(personaId: string): void {
    const currentPersonas = this.personasSubject.value;
    const filteredPersonas = currentPersonas.filter(persona => persona.id !== personaId);
    this.personasSubject.next(filteredPersonas);

    // If deleted persona was selected, select the first available persona
    const selectedPersona = this.selectedPersonaSubject.value;
    if (selectedPersona && selectedPersona.id === personaId) {
      this.selectedPersonaSubject.next(filteredPersonas.length > 0 ? filteredPersonas[0] : null);
    }
  }

  // Selected persona operations
  setSelectedPersona(personaId: string): void {
    const persona = this.getPersonaById(personaId);
    if (persona) {
      this.selectedPersonaSubject.next(persona);
    }
  }

  getSelectedPersona(): PersonaData | null {
    return this.selectedPersonaSubject.value;
  }

  // Card-specific operations for persona details
  updatePersonaCard(personaId: string, cardType: PersonaCard['type'], data: any): void {
    const updates: Partial<PersonaData> = {};
    updates[cardType] = data;
    this.updatePersona(personaId, updates);
  }

  // Utility methods
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  getDefaultAvatar(role: string): string {
    const r = role.toLowerCase();
    if (r.includes('manager')) return '/svgs/manager-avatar.svg';
    if (r.includes('designer')) return '/svgs/designer-avatar.svg';
    if (r.includes('teacher')) return '/svgs/teacher-avatar.svg';
    if (r.includes('developer')) return '/svgs/developer-avatar.svg';
    return '/svgs/manager-avatar.svg';
  }

  // Convert legacy UserPersona to PersonaData
  convertUserPersonaToPersonaData(userPersona: any): Omit<PersonaData, 'id'> {
    return {
      name: userPersona.role || 'Unknown',
      role: userPersona.role || 'Unknown',
      age: userPersona.age || 0,
      education: userPersona.education || '',
      status: userPersona.status || '',
      location: userPersona.location || '',
      techLiteracy: userPersona.techLiteracy || 'Medium',
      quote: userPersona.quote || '',
      personality: userPersona.personality || [],
      avatar: userPersona.avatar || this.getDefaultAvatar(userPersona.role || ''),
      painPoints: [],
      goals: [],
      motivation: [],
      expectations: [],
      skills: [],
      devices: ['mobile']
    };
  }
}
