<div class="brainstorming-container p-4">
  <!-- <PERSON> Header -->
  <!-- <div class="page-header mb-4">
    <h1 class="page-title mb-1">{{ currentStep?.label || 'Brainstorming' }}</h1>
  </div> -->

  <!-- Custom Stepper -->
  <section class="stepper-section">
    <app-brainstormer-stepper
      [showDescriptions]="false"
      [showProgressBar]="false"
      [allowStepClick]="false"
      (stepChanged)="onStepChanged($event)"
    >
    </app-brainstormer-stepper>
  </section>

  <section>
    <div class="sub-header">
      <awe-heading variant="h5" type="bold" class="sub-header-title">
        {{ currentStep?.label || "Brainstorming" }}
      </awe-heading>

      <div class="sub-header-actions">
        <button (click)="previousStep()" [disabled]="!canGoPrevious">
          <awe-icons class="next-icon" iconName="awe_chevron_left"></awe-icons>
        </button>
        <button (click)="nextStep()" [disabled]="!canGoNext">
          <awe-icons
            class="previous-icon"
            iconName="awe_chevron_right"
          ></awe-icons>
        </button>
      </div>
    </div>
  </section>

  <!-- Split Screen View -->
  <awe-split-screen
    *ngIf="showSplitScreen"
    [leftPanelWidth]="22"
    [toggle]="showSplitScreen"
    [theme]="isDarkMode ? 'dark' : 'light'"
    (toggleChange)="showSplitScreen = $event"
    (onClose)="onSplitScreenClose()"
    (onOpen)="onSplitScreenOpen()"
    class="split-screen-container mt-4"
  >
    <!-- Left Panel Content (Chat) -->
    <div slot="left-panel">
      <app-chat-panel (closeSplitScreen)="toggleSplitScreen()"></app-chat-panel>
    </div>

    <!-- Right Panel Content (Main App) -->
    <div slot="right-panel" class="main-content">
      <div class="content-container">
        <!-- Understanding Component -->
        <div *ngIf="shouldShowComponent('understanding')" class="step-content">
          <app-understanding></app-understanding>
        </div>

        <!-- User Persona Component -->
        <div *ngIf="shouldShowPersonaList()" class="step-content">
          <app-user-persona
            (personaSelected)="showPersonaDetailsView($event)"
          ></app-user-persona>
        </div>

        <!-- Persona Details Component -->
        <div *ngIf="shouldShowPersonaDetails()" class="step-content">
          <app-persona-details
            (backToList)="hidePersonaDetailsView()"
          ></app-persona-details>
        </div>

        <!-- Feature List Component -->
        <div *ngIf="shouldShowComponent('features')" class="step-content">
          <app-feature-list></app-feature-list>
        </div>

        <!-- SWOT Analysis Component (Placeholder) -->
        <div *ngIf="shouldShowComponent('swot')" class="step-content">
          <app-swot-analysis></app-swot-analysis>
        </div>

        <!-- Product Roadmap Component (Placeholder) -->
        <div *ngIf="shouldShowComponent('roadmap')" class="step-content">
          <app-product-roadmap></app-product-roadmap>
        </div>
      </div>
    </div>
  </awe-split-screen>

  <!-- Regular View (when split screen is not active) -->

  <div class="content-container" *ngIf="!showSplitScreen">
    <!-- Understanding Component -->
    <div *ngIf="shouldShowComponent('understanding')" class="step-content">
      <app-understanding></app-understanding>
    </div>

    <!-- User Persona Component -->
    <div *ngIf="shouldShowPersonaList()" class="step-content">
      <app-user-persona
        (personaSelected)="showPersonaDetailsView($event)"
      ></app-user-persona>
    </div>

    <!-- Persona Details Component -->
    <div *ngIf="shouldShowPersonaDetails()" class="step-content">
      <app-persona-details
        (backToList)="hidePersonaDetailsView()"
      ></app-persona-details>
    </div>

    <!-- Feature List Component -->
    <div *ngIf="shouldShowComponent('features')" class="step-content">
      <app-feature-list></app-feature-list>
    </div>

    <!-- SWOT Analysis Component (Placeholder) -->
    <div *ngIf="shouldShowComponent('swot')" class="step-content">
      <app-swot-analysis></app-swot-analysis>
    </div>

    <!-- Product Roadmap Component (Placeholder) -->
    <div *ngIf="shouldShowComponent('roadmap')" class="step-content">
      <app-product-roadmap></app-product-roadmap>
    </div>
  </div>

  <!-- Navigation Buttons -->
  <div
    class="ai-assistant-prompt"
    *ngIf="!showSplitScreen"
    [@promptAnimation]="promptState"
  >
    <div
      class="ai-assistant-icon"
      (click)="toggleSplitScreen()"
      (mouseenter)="onRoboBallHover()"
      (mouseleave)="onRoboBallLeave()"
      [class.chat-open]="showSplitScreen"
      [@roboBallAnimation]="roboBallState"
    >
      <img [src]="roboBallIcon" alt="AI Assistant" />
    </div>
    <p class="ai-assistant-text">Hi there - need a hand?</p>
  </div>
</div>
